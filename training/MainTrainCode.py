# 🚀 TRAIN YOUR OWN LANGUAGE MODEL FROM SCRATCH - GOOGLE COLAB OPTIMIZED 🚀
# No GPT-2, no pretrained anything - 100% your own model!
# Automatically saves everything to Google Drive for persistence

# GOOGLE COLAB SETUP CELL - RUN THIS FIRST!
"""
# Uncomment and run this cell first in Google Colab:

# Install required packages
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
!pip install tqdm matplotlib numpy

# Check GPU
import torch
print(f"🔥 CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"💾 GPU: {torch.cuda.get_device_name()}")
    print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import gc
from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import json
import re
from collections import Counter, defaultdict
import shutil
from datetime import datetime

# Google Colab specific imports
try:
    from google.colab import drive, files
    IN_COLAB = True
    print("🔥 GOOGLE COLAB DETECTED!")
except ImportError:
    IN_COLAB = False
    print("🔥 RUNNING LOCALLY")

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 ENVIRONMENT CHECK:")
    print(f"✅ Python version: {__import__('sys').version}")
    print(f"✅ PyTorch version: {torch.__version__}")
    print(f"✅ CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name()}")
        print(f"✅ GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Test GPU
        try:
            test_tensor = torch.randn(100, 100).cuda()
            result = torch.mm(test_tensor, test_tensor)
            print("✅ GPU test passed!")
            del test_tensor, result
            torch.cuda.empty_cache()
        except Exception as e:
            print(f"❌ GPU test failed: {e}")
    else:
        print("⚠️ No GPU detected - training will be slow")

    print("✅ Environment check complete!")

# Run environment check
check_environment()

print("🔥 TRAIN YOUR OWN MODEL FROM SCRATCH!")
print(f"💾 GPU: {torch.cuda.get_device_name() if torch.cuda.is_available() else 'CPU only'}")
print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB" if torch.cuda.is_available() else "")

# Google Drive Integration
def setup_google_drive():
    """Mount Google Drive and create project directory"""
    if not IN_COLAB:
        print("⚠️ Not in Colab - using local directory")
        project_dir = "./custom_model_training"
        os.makedirs(project_dir, exist_ok=True)
        return project_dir

    try:
        print("📁 Mounting Google Drive...")
        drive.mount('/content/drive')

        # Create project directory in Drive
        project_dir = "/content/drive/MyDrive/CustomModelTraining"
        os.makedirs(project_dir, exist_ok=True)

        # Create subdirectories
        subdirs = ['models', 'tokenizers', 'checkpoints', 'logs', 'plots']
        for subdir in subdirs:
            os.makedirs(f"{project_dir}/{subdir}", exist_ok=True)

        print(f"✅ Google Drive mounted! Project directory: {project_dir}")
        return project_dir

    except Exception as e:
        print(f"❌ Failed to mount Google Drive: {e}")
        print("📁 Using local directory instead")
        project_dir = "./custom_model_training"
        os.makedirs(project_dir, exist_ok=True)
        return project_dir

# Initialize project directory
PROJECT_DIR = setup_google_drive()

# CUSTOM TOKENIZER - BUILD YOUR OWN VOCAB! 🛠️
class CustomTokenizer:
    def __init__(self):
        self.word_to_id = {}
        self.id_to_word = {}
        self.vocab_size = 0
        
        # Special tokens
        self.special_tokens = {
            '<PAD>': 0,
            '<UNK>': 1, 
            '<SOS>': 2,
            '<EOS>': 3,
        }
        
        for token, idx in self.special_tokens.items():
            self.word_to_id[token] = idx
            self.id_to_word[idx] = token
        
        self.vocab_size = len(self.special_tokens)
        print("🔧 Custom tokenizer initialized with special tokens")
    
    def build_vocab(self, texts, min_freq=2, max_vocab=10000):
        """Build vocabulary from your texts"""
        print("📚 Building vocabulary from scratch...")
        
        # Count word frequencies
        word_counts = Counter()
        total_words = 0
        
        for text in tqdm(texts, desc="Counting words"):
            # Simple word tokenization (you can make this fancier)
            words = self.simple_tokenize(text.lower())
            word_counts.update(words)
            total_words += len(words)
        
        print(f"📊 Found {len(word_counts)} unique words, {total_words:,} total words")
        
        # Filter by frequency and limit vocab size
        filtered_words = [word for word, count in word_counts.items() 
                         if count >= min_freq]
        
        # Sort by frequency and take top words
        most_common = word_counts.most_common(max_vocab - len(self.special_tokens))
        
        # Add words to vocabulary
        for word, count in most_common:
            if word not in self.word_to_id:
                self.word_to_id[word] = self.vocab_size
                self.id_to_word[self.vocab_size] = word
                self.vocab_size += 1
        
        print(f"🎯 Final vocabulary size: {self.vocab_size:,} tokens")
        print(f"📈 Coverage: {len(most_common)} words above min_freq={min_freq}")
        
        # Show some examples
        print("\n🔤 Sample vocabulary:")
        for i, (word, count) in enumerate(most_common[:20]):
            print(f"  {self.word_to_id[word]:4d}: '{word}' (count: {count})")
        
        return self.vocab_size
    
    def simple_tokenize(self, text):
        """Simple but effective tokenization"""
        # Clean and split text
        text = re.sub(r'[^\w\s.,!?;:\'"()-]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        # Split into words and punctuation
        tokens = []
        for word in text.split():
            # Split punctuation from words
            word = word.strip()
            if word:
                # Handle punctuation at the end
                while word and word[-1] in '.,!?;:':
                    if len(word) > 1:
                        tokens.append(word[:-1])
                        tokens.append(word[-1])
                        word = ""
                    else:
                        tokens.append(word)
                        word = ""
                
                if word:
                    tokens.append(word)
        
        return tokens
    
    def encode(self, text):
        """Convert text to token IDs"""
        words = self.simple_tokenize(text.lower())
        token_ids = []
        
        for word in words:
            token_ids.append(self.word_to_id.get(word, self.special_tokens['<UNK>']))
        
        return token_ids
    
    def decode(self, token_ids):
        """Convert token IDs back to text"""
        words = []
        for token_id in token_ids:
            if token_id in self.id_to_word:
                word = self.id_to_word[token_id]
                if word not in ['<PAD>', '<SOS>', '<EOS>']:
                    words.append(word)
        
        # Simple reconstruction
        text = ' '.join(words)
        # Fix punctuation spacing
        text = re.sub(r' ([.,!?;:])', r'\1', text)
        return text
    
    def save(self, filename=None):
        """Save tokenizer to Google Drive"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"custom_tokenizer_{timestamp}.json"

        # Save to project directory
        path = f"{PROJECT_DIR}/tokenizers/{filename}"

        vocab_data = {
            'word_to_id': self.word_to_id,
            'id_to_word': {str(k): v for k, v in self.id_to_word.items()},
            'vocab_size': self.vocab_size,
            'special_tokens': self.special_tokens,
            'created_at': datetime.now().isoformat(),
            'total_tokens_processed': sum(len(self.simple_tokenize(text)) for text in [])  # Will be updated during training
        }

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)

        # Also save a backup with simple name
        backup_path = f"{PROJECT_DIR}/tokenizers/latest_tokenizer.json"
        shutil.copy2(path, backup_path)

        print(f"💾 Tokenizer saved to {path}")
        print(f"💾 Backup saved to {backup_path}")
        return path

    def load(self, path):
        """Load tokenizer from file"""
        # Try different possible paths
        possible_paths = [
            path,
            f"{PROJECT_DIR}/tokenizers/{path}",
            f"{PROJECT_DIR}/tokenizers/latest_tokenizer.json"
        ]

        for try_path in possible_paths:
            if os.path.exists(try_path):
                with open(try_path, 'r', encoding='utf-8') as f:
                    vocab_data = json.load(f)

                self.word_to_id = vocab_data['word_to_id']
                self.id_to_word = {int(k): v for k, v in vocab_data['id_to_word'].items()}
                self.vocab_size = vocab_data['vocab_size']
                self.special_tokens = vocab_data['special_tokens']
                print(f"📁 Tokenizer loaded from {try_path}")
                return try_path

        raise FileNotFoundError(f"Could not find tokenizer at any of: {possible_paths}")

# YOUR OWN TRANSFORMER MODEL 🤖
class CustomLanguageModel(nn.Module):
    def __init__(self, vocab_size, dim=384, n_layers=6, n_heads=6, max_seq_len=256):
        super().__init__()
        self.vocab_size = vocab_size
        self.dim = dim
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.tok_emb = nn.Embedding(vocab_size, dim)
        self.pos_emb = nn.Embedding(max_seq_len, dim)
        self.dropout = nn.Dropout(0.1)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(dim, n_heads) for _ in range(n_layers)
        ])
        
        self.ln_f = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, vocab_size)
        
        # Initialize weights
        self.apply(self._init_weights)
        
        print(f"🤖 Custom model created: {self.count_params():,} parameters")
        print(f"📐 Architecture: {n_layers} layers, {n_heads} heads, {dim} dim")
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def count_params(self):
        return sum(p.numel() for p in self.parameters())
    
    def forward(self, x, targets=None):
        B, T = x.shape
        
        # Get embeddings
        tok_emb = self.tok_emb(x)
        pos_emb = self.pos_emb(torch.arange(T, device=x.device))
        x = self.dropout(tok_emb + pos_emb)
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)
        
        x = self.ln_f(x)
        logits = self.head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=0)
        
        return logits, loss

class TransformerBlock(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attn = MultiHeadAttention(dim, n_heads)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim),
            nn.Dropout(0.1)
        )
        self.ln1 = nn.LayerNorm(dim)
        self.ln2 = nn.LayerNorm(dim)
        self.dropout1 = nn.Dropout(0.1)
        self.dropout2 = nn.Dropout(0.1)
    
    def forward(self, x):
        x = x + self.dropout1(self.attn(self.ln1(x)))
        x = x + self.dropout2(self.mlp(self.ln2(x)))
        return x

class MultiHeadAttention(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        assert dim % n_heads == 0
        
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        B, T, C = x.shape
        
        # Get Q, K, V
        qkv = self.qkv(x)
        q, k, v = qkv.chunk(3, dim=-1)
        
        # Reshape for multi-head attention
        q = q.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        k = k.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        v = v.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        att = (q @ k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        
        # Causal mask
        mask = torch.triu(torch.ones(T, T, device=x.device), diagonal=1).bool()
        att.masked_fill_(mask, float('-inf'))
        
        att = F.softmax(att, dim=-1)
        att = self.dropout(att)
        
        # Apply attention to values
        out = att @ v
        out = out.transpose(1, 2).contiguous().view(B, T, C)
        
        return self.proj(out)

# CUSTOM DATASET FOR YOUR MODEL
class CustomDataset(Dataset):
    def __init__(self, texts, tokenizer, seq_len=256):
        print("📝 Creating custom dataset...")
        self.seq_len = seq_len
        self.tokenizer = tokenizer
        
        # Tokenize all texts
        all_tokens = []
        for text in tqdm(texts, desc="Tokenizing"):
            tokens = tokenizer.encode(text)
            all_tokens.extend(tokens)
        
        print(f"📊 Total tokens: {len(all_tokens):,}")
        
        # Create sequences
        self.data = []
        for i in range(0, len(all_tokens) - seq_len, seq_len // 2):  # 50% overlap
            chunk = all_tokens[i:i + seq_len + 1]
            if len(chunk) == seq_len + 1:
                self.data.append(torch.tensor(chunk, dtype=torch.long))
        
        print(f"📦 Dataset ready: {len(self.data)} sequences of length {seq_len}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        chunk = self.data[idx]
        return chunk[:-1], chunk[1:]  # input, target

# FREE COLAB SETTINGS - OPTIMIZED FOR YOUR MODEL
def get_optimal_settings():
    """Get settings optimized for custom model on free Colab"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"🔍 GPU Memory: {gpu_memory:.1f} GB")
        
        if gpu_memory < 13:  # T4 GPU (free Colab)
            return {
                'batch_size': 8,
                'seq_len': 256,
                'model_dim': 384,
                'n_layers': 6,
                'n_heads': 6,
                'grad_accum': 2,
                'max_vocab': 8000
            }
        else:  # Better GPU
            return {
                'batch_size': 16,
                'seq_len': 512,
                'model_dim': 512,
                'n_layers': 8,
                'n_heads': 8,
                'grad_accum': 1,
                'max_vocab': 15000
            }
    else:  # CPU
        return {
            'batch_size': 2,
            'seq_len': 128,
            'model_dim': 256,
            'n_layers': 4,
            'n_heads': 4,
            'grad_accum': 4,
            'max_vocab': 5000
        }

# CUSTOM TRAINER - TRACKS EVERYTHING WITH GOOGLE DRIVE INTEGRATION
class CustomTrainer:
    def __init__(self, model, tokenizer, device, project_dir=None):
        self.model = model.to(device)
        self.tokenizer = tokenizer
        self.device = device
        self.losses = []
        self.step_times = []
        self.project_dir = project_dir or PROJECT_DIR
        self.training_start_time = None
        self.checkpoint_frequency = 500  # Save checkpoint every N steps

        # Create training log
        self.log_file = f"{self.project_dir}/logs/training_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.log(f"🚀 Custom trainer initialized on {device}")
        self.log(f"📊 Model parameters: {model.count_params():,}")

        print(f"🚀 Custom trainer ready on {device}")
        print(f"📝 Training log: {self.log_file}")

    def log(self, message):
        """Log message to file and console"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"

        # Write to log file
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

    def save_checkpoint(self, epoch, step, loss, optimizer, scheduler, is_best=False):
        """Save training checkpoint to Google Drive"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        checkpoint = {
            'epoch': epoch,
            'step': step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
            'loss': loss,
            'losses_history': self.losses,
            'step_times': self.step_times,
            'vocab_size': self.tokenizer.vocab_size,
            'model_config': {
                'vocab_size': self.model.vocab_size,
                'dim': self.model.dim,
                'max_seq_len': self.model.max_seq_len
            },
            'timestamp': timestamp
        }

        # Save checkpoint
        checkpoint_path = f"{self.project_dir}/checkpoints/checkpoint_epoch_{epoch}_step_{step}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Save as latest checkpoint
        latest_path = f"{self.project_dir}/checkpoints/latest_checkpoint.pt"
        torch.save(checkpoint, latest_path)

        if is_best:
            best_path = f"{self.project_dir}/models/best_model.pt"
            torch.save(checkpoint, best_path)
            self.log(f"💾 New best model saved! Loss: {loss:.4f}")

        self.log(f"💾 Checkpoint saved: {checkpoint_path}")
        return checkpoint_path
    
    def train(self, dataset, epochs=5, lr=3e-4, settings=None, resume_from_checkpoint=None):
        if settings is None:
            settings = get_optimal_settings()

        self.log(f"🎯 Training settings: {settings}")
        print(f"🎯 Training settings: {settings}")

        # Setup
        dataloader = DataLoader(dataset, batch_size=settings['batch_size'], shuffle=True, pin_memory=True)
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)

        # Learning rate scheduler
        total_steps = len(dataloader) * epochs
        warmup_steps = min(500, max(1, total_steps // 10))  # Ensure at least 1 step

        # Ensure we have enough steps for the scheduler
        if total_steps <= 1:
            scheduler = None
            self.log("⚠️ Too few steps for scheduler, using constant learning rate")
        else:
            scheduler = torch.optim.lr_scheduler.OneCycleLR(
                optimizer, max_lr=lr, total_steps=total_steps,
                pct_start=max(0.01, warmup_steps/total_steps)  # Ensure valid percentage
            )

        # Resume from checkpoint if provided
        start_epoch = 0
        global_step = 0
        best_loss = float('inf')

        if resume_from_checkpoint:
            checkpoint = torch.load(resume_from_checkpoint, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            if checkpoint.get('scheduler_state_dict') and scheduler is not None:
                try:
                    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                except Exception as e:
                    self.log(f"⚠️ Could not load scheduler state: {e}")
                    print(f"⚠️ Could not load scheduler state, creating new scheduler")
            start_epoch = checkpoint['epoch']
            global_step = checkpoint['step']
            best_loss = checkpoint['loss']
            self.losses = checkpoint.get('losses_history', [])
            self.step_times = checkpoint.get('step_times', [])
            self.log(f"� Resumed from checkpoint: epoch {start_epoch}, step {global_step}")
            print(f"📁 Resumed from checkpoint: epoch {start_epoch}, step {global_step}")

        self.training_start_time = time.time()

        self.log(f"🚀 Starting training from epoch {start_epoch+1}")
        self.log(f"📊 {epochs} epochs × {len(dataloader)} batches = {total_steps:,} steps")
        self.log(f"🔥 Effective batch size: {settings['batch_size'] * settings['grad_accum']}")

        print(f"🚀 Starting training!")
        print(f"📊 {epochs} epochs × {len(dataloader)} batches = {total_steps:,} steps")
        print(f"🔥 Effective batch size: {settings['batch_size'] * settings['grad_accum']}")

        # Training loop
        self.model.train()

        for epoch in range(start_epoch, epochs):
            epoch_loss = 0
            epoch_start = time.time()

            pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")
            optimizer.zero_grad()

            for batch_idx, (inputs, targets) in enumerate(pbar):
                step_start = time.time()

                inputs = inputs.to(self.device, non_blocking=True)
                targets = targets.to(self.device, non_blocking=True)

                # Forward pass
                logits, loss = self.model(inputs, targets)

                # Scale loss for gradient accumulation
                loss = loss / settings['grad_accum']
                loss.backward()

                epoch_loss += loss.item() * settings['grad_accum']

                # Gradient step
                if (batch_idx + 1) % settings['grad_accum'] == 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    optimizer.step()
                    if scheduler is not None:
                        scheduler.step()
                    optimizer.zero_grad()
                    global_step += 1

                    # Save checkpoint periodically
                    if global_step % self.checkpoint_frequency == 0:
                        current_loss = epoch_loss / (batch_idx + 1)
                        self.save_checkpoint(epoch, global_step, current_loss, optimizer, scheduler)

                # Track timing
                step_time = time.time() - step_start
                self.step_times.append(step_time)

                # Update progress
                avg_loss = epoch_loss / (batch_idx + 1)
                current_lr = optimizer.param_groups[0]['lr']

                pbar.set_postfix({
                    'loss': f'{avg_loss:.4f}',
                    'lr': f'{current_lr:.2e}',
                    'step/s': f'{1/step_time:.1f}',
                    'step': global_step
                })

                # Memory cleanup
                if batch_idx % 50 == 0:
                    torch.cuda.empty_cache()
                    gc.collect()

            # Epoch summary
            epoch_time = time.time() - epoch_start
            avg_epoch_loss = epoch_loss / len(dataloader)
            self.losses.append(avg_epoch_loss)

            epoch_summary = f"✅ Epoch {epoch+1}: Loss={avg_epoch_loss:.4f}, Time={epoch_time:.1f}s"
            print(epoch_summary)
            self.log(epoch_summary)

            # Save checkpoint at end of epoch
            is_best = avg_epoch_loss < best_loss
            if is_best:
                best_loss = avg_epoch_loss

            self.save_checkpoint(epoch, global_step, avg_epoch_loss, optimizer, scheduler, is_best)

            # Save training plot
            if (epoch + 1) % 2 == 0:  # Every 2 epochs
                self.save_training_plot()

        total_time = time.time() - self.training_start_time
        final_summary = f"🎉 Training complete! Total time: {total_time/60:.1f} minutes"
        print(final_summary)
        self.log(final_summary)
        self.log(f"📊 Final loss: {best_loss:.4f}")
        self.log(f"📊 Total steps: {global_step}")

        # Save final model and tokenizer
        self.save_final_model()
        self.tokenizer.save()

        return {
            'losses': self.losses,
            'best_loss': best_loss,
            'total_time': total_time,
            'avg_step_time': np.mean(self.step_times),
            'total_steps': global_step
        }
    
    def generate(self, prompt, max_length=50, temperature=0.8, top_k=40):
        """Generate text with your custom model!"""
        self.model.eval()
        
        tokens = self.tokenizer.encode(prompt)
        if not tokens:  # Empty prompt
            tokens = [self.tokenizer.special_tokens['<SOS>']]
        
        tokens = torch.tensor(tokens, device=self.device).unsqueeze(0)
        
        generated_tokens = []
        
        with torch.no_grad():
            for _ in range(max_length):
                # Truncate if too long
                if tokens.size(1) >= self.model.max_seq_len:
                    tokens = tokens[:, -(self.model.max_seq_len-1):]
                
                logits, _ = self.model(tokens)
                logits = logits[0, -1, :] / temperature
                
                # Top-k sampling
                if top_k > 0:
                    values, indices = torch.topk(logits, top_k)
                    logits = torch.full_like(logits, float('-inf'))
                    logits.scatter_(0, indices, values)
                
                probs = F.softmax(logits, dim=-1)
                next_token = torch.multinomial(probs, 1)
                
                # Stop at EOS token
                if next_token.item() == self.tokenizer.special_tokens['<EOS>']:
                    break
                
                tokens = torch.cat([tokens, next_token.unsqueeze(0)], dim=1)
                generated_tokens.append(next_token.item())
        
        # Decode generated text
        full_tokens = self.tokenizer.encode(prompt) + generated_tokens
        generated_text = self.tokenizer.decode(full_tokens)
        
        self.model.train()
        return generated_text

    def save_final_model(self):
        """Save the final trained model"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save model state dict
        model_path = f"{self.project_dir}/models/final_model_{timestamp}.pt"
        torch.save(self.model.state_dict(), model_path)

        # Save complete model info
        model_info = {
            'model_state_dict': self.model.state_dict(),
            'model_config': {
                'vocab_size': self.model.vocab_size,
                'dim': self.model.dim,
                'max_seq_len': self.model.max_seq_len
            },
            'training_losses': self.losses,
            'final_loss': self.losses[-1] if self.losses else None,
            'total_parameters': self.model.count_params(),
            'timestamp': timestamp
        }

        complete_model_path = f"{self.project_dir}/models/complete_model_{timestamp}.pt"
        torch.save(model_info, complete_model_path)

        # Save as latest
        latest_model_path = f"{self.project_dir}/models/latest_model.pt"
        torch.save(model_info, latest_model_path)

        self.log(f"💾 Final model saved to {model_path}")
        self.log(f"💾 Complete model info saved to {complete_model_path}")

        return model_path

    def save_training_plot(self):
        """Save training progress plot to Google Drive"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # Loss curve
        if self.losses:
            ax1.plot(self.losses, 'b-', alpha=0.8, linewidth=2)
            ax1.set_title('Training Loss')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('Loss')
            ax1.grid(True, alpha=0.3)

            # Add best loss annotation
            if self.losses:
                best_epoch = np.argmin(self.losses)
                best_loss = self.losses[best_epoch]
                ax1.annotate(f'Best: {best_loss:.4f}',
                           xy=(best_epoch, best_loss),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        # Step timing
        if self.step_times:
            recent_times = self.step_times[-100:]  # Last 100 steps
            ax2.hist(recent_times, bins=20, alpha=0.7, color='green')
            ax2.set_title('Step Time Distribution (Last 100 steps)')
            ax2.set_xlabel('Seconds per Step')
            ax2.set_ylabel('Count')
            ax2.grid(True, alpha=0.3)

            # Add average time annotation
            avg_time = np.mean(recent_times)
            ax2.axvline(avg_time, color='red', linestyle='--', alpha=0.8)
            ax2.text(avg_time, ax2.get_ylim()[1] * 0.8, f'Avg: {avg_time:.3f}s',
                    rotation=90, verticalalignment='bottom')

        plt.tight_layout()

        # Save plot
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plot_path = f"{self.project_dir}/plots/training_progress_{timestamp}.png"
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')

        # Save as latest
        latest_plot_path = f"{self.project_dir}/plots/latest_training_progress.png"
        plt.savefig(latest_plot_path, dpi=150, bbox_inches='tight')

        if IN_COLAB:
            plt.show()
        else:
            plt.close()

        self.log(f"📊 Training plot saved to {plot_path}")
        return plot_path

    def plot_training(self):
        """Plot training progress (legacy method for compatibility)"""
        return self.save_training_plot()

# SAMPLE TRAINING DATA - CUSTOMIZE THIS!
CUSTOM_TRAINING_DATA = [
    """
    The art of machine learning involves finding patterns in data that can generalize to new, unseen examples.
    Neural networks are powerful function approximators that can learn complex mappings between inputs and outputs.
    Deep learning uses multiple layers of neurons to learn hierarchical representations of data.
    Training a neural network involves adjusting weights through backpropagation and gradient descent.
    """,
    
    """
    Natural language processing enables computers to understand and generate human language.
    Transformers revolutionized NLP by using attention mechanisms to capture long-range dependencies.
    Language models learn to predict the next word in a sequence given the previous context.
    The quality of a language model depends on the size and diversity of its training data.
    """,
    
    """
    Artificial intelligence aims to create machines that can perform tasks requiring human intelligence.
    Machine learning is a subset of AI that learns from data without explicit programming.
    Supervised learning uses labeled examples to train predictive models.
    Unsupervised learning finds hidden patterns in unlabeled data.
    Reinforcement learning trains agents through rewards and penalties.
    """,
    
    """
    Computer vision enables machines to interpret and understand visual information.
    Convolutional neural networks are particularly effective for image recognition tasks.
    Object detection identifies and localizes multiple objects within images.
    Image segmentation assigns a class label to each pixel in an image.
    """,
    
    """
    The future of artificial intelligence holds great promise and challenges.
    Ethical AI development ensures that technology benefits humanity fairly.
    Explainable AI makes machine learning decisions more transparent and trustworthy.
    Edge AI brings intelligence to devices without requiring cloud connectivity.
    Quantum computing may revolutionize certain types of machine learning algorithms.
    """,
    
    # Add your own training data here!
    """
    Your custom training data goes here. Add as much text as you want!
    The model will learn the patterns, vocabulary, and style from this data.
    Make sure to include diverse examples of the kind of text you want to generate.
    """

    """
    Language models like GPT are trained on vast amounts of text to predict the next word in a sequence.
    They capture syntax, semantics, and even subtle nuances of human expression through probabilistic modeling.
    Fine-tuning adapts these general models to specific tasks or domains, such as legal writing or medical summaries.
    Transfer learning allows the model to leverage general knowledge in specialized contexts.
    """,
    """
    In ancient philosophy, the nature of knowledge was debated among scholars who sought truth through reason and dialogue.
    Socratic questioning remains a powerful method of uncovering assumptions and stimulating critical thinking.
    Logic and rhetoric formed the backbone of classical education, emphasizing clarity and persuasive argumentation.
    Modern applications of these disciplines can be found in law, ethics, and public discourse.
    """,
    """
    The ecosystem of a rainforest teems with biodiversity, from towering canopy trees to microscopic fungi in the soil.
    Each organism plays a role in maintaining balance, whether as predator, prey, decomposer, or producer.
    Human encroachment threatens these ecosystems, prompting conservation efforts around the globe.
    Environmental science seeks to understand and preserve the intricate web of life on Earth.
    """,
    """
    In software engineering, modular design enables developers to build reusable, maintainable components.
    Version control systems like Git facilitate collaboration and tracking of code changes across teams.
    Agile methodologies prioritize iterative development, user feedback, and continuous integration.
    Writing clean, well-documented code is as important as implementing efficient algorithms.
    """,
    """
    A haiku captures fleeting beauty in just seventeen syllables, distilling nature's essence into compact verse.
    Poetry can be freeform or structured, reflective or explosive, deeply personal or universally resonant.
    The rhythm of language, its cadence and flow, transforms simple words into emotional experiences.
    Writers often draw from metaphor and imagery to convey layers of meaning in just a few lines.
    """
] * 10  # Repeat for more training data

def train_your_model(resume_from_checkpoint=None, custom_data=None):
    """🚀 THE MAIN FUNCTION - TRAIN YOUR OWN MODEL FROM SCRATCH!"""
    print("🔥 TRAINING YOUR OWN MODEL FROM SCRATCH!")
    print("=" * 60)

    # Setup Google Drive
    project_dir = setup_google_drive()

    # Get optimal settings
    settings = get_optimal_settings()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    print(f"📁 Project directory: {project_dir}")
    print(f"🔧 Device: {device}")

    # Use custom data if provided, otherwise use default
    training_data = custom_data if custom_data is not None else CUSTOM_TRAINING_DATA

    # Step 1: Build custom tokenizer
    print("\n📚 STEP 1: Building custom vocabulary...")
    tokenizer = CustomTokenizer()

    # Try to load existing tokenizer if resuming
    if resume_from_checkpoint:
        try:
            tokenizer.load("latest_tokenizer.json")
            vocab_size = tokenizer.vocab_size
            print(f"📁 Loaded existing tokenizer with {vocab_size:,} tokens")
        except FileNotFoundError:
            print("⚠️ No existing tokenizer found, building new one...")
            vocab_size = tokenizer.build_vocab(training_data, min_freq=2, max_vocab=settings['max_vocab'])
            tokenizer.save()
    else:
        vocab_size = tokenizer.build_vocab(training_data, min_freq=2, max_vocab=settings['max_vocab'])
        tokenizer.save()

    # Step 2: Create custom model
    print(f"\n🤖 STEP 2: Creating your model...")
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=settings['model_dim'],
        n_layers=settings['n_layers'],
        n_heads=settings['n_heads'],
        max_seq_len=settings['seq_len']
    )

    # Step 3: Prepare dataset
    print("\n📦 STEP 3: Preparing training dataset...")
    dataset = CustomDataset(training_data, tokenizer, settings['seq_len'])

    # Step 4: Train the model
    print("\n🚀 STEP 4: Training your model...")
    trainer = CustomTrainer(model, tokenizer, device, project_dir)

    # Estimate training time
    estimated_time = len(dataset) * settings.get('grad_accum', 1) / settings['batch_size'] * 0.1 * 5  # 5 epochs
    print(f"⏰ Estimated training time: {estimated_time/60:.1f} minutes")

    # Check for existing checkpoint
    if resume_from_checkpoint is None:
        latest_checkpoint = f"{project_dir}/checkpoints/latest_checkpoint.pt"
        if os.path.exists(latest_checkpoint):
            response = input("🔄 Found existing checkpoint. Resume training? (y/n): ").lower()
            if response == 'y':
                resume_from_checkpoint = latest_checkpoint

    results = trainer.train(dataset, epochs=5, lr=3e-4, settings=settings,
                          resume_from_checkpoint=resume_from_checkpoint)

    # Step 5: Test generation
    print("\n🎭 STEP 5: Testing your model...")
    test_prompts = [
        "The future of",
        "Machine learning",
        "Artificial intelligence",
        "Neural networks",
        "Deep learning is"
    ]

    print("Generated samples:")
    print("-" * 40)
    generation_results = []
    for prompt in test_prompts:
        generated = trainer.generate(prompt, max_length=30, temperature=0.8)
        print(f"Prompt: '{prompt}'")
        print(f"Generated: '{generated}'")
        print()
        generation_results.append((prompt, generated))

    # Save generation results
    generation_log = f"{project_dir}/logs/generation_samples_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(generation_log, 'w', encoding='utf-8') as f:
        f.write("Generation Test Results\n")
        f.write("=" * 50 + "\n\n")
        for prompt, generated in generation_results:
            f.write(f"Prompt: {prompt}\n")
            f.write(f"Generated: {generated}\n\n")

    # Step 6: Show results
    print("📊 TRAINING SUMMARY:")
    print(f"✅ Final loss: {results['losses'][-1]:.4f}")
    print(f"🏆 Best loss: {results['best_loss']:.4f}")
    print(f"⏰ Training time: {results['total_time']/60:.1f} minutes")
    print(f"⚡ Average step time: {results['avg_step_time']:.3f}s")
    print(f"📈 Total steps: {results['total_steps']:,}")
    print(f"🤖 Model parameters: {model.count_params():,}")
    print(f"📚 Vocabulary size: {vocab_size:,}")
    print(f"📁 All files saved to: {project_dir}")

    # Plot training curves
    trainer.plot_training()

    # Save final summary
    summary = {
        'final_loss': results['losses'][-1],
        'best_loss': results['best_loss'],
        'training_time_minutes': results['total_time']/60,
        'total_steps': results['total_steps'],
        'model_parameters': model.count_params(),
        'vocab_size': vocab_size,
        'settings': settings,
        'generation_samples': generation_results
    }

    summary_path = f"{project_dir}/logs/training_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"📋 Training summary saved to: {summary_path}")

    return trainer, model, tokenizer, results

def quick_demo():
    """Quick demo with minimal settings"""
    print("⚡ QUICK DEMO MODE - Minimal training for testing")
    
    # Use smaller settings for quick demo
    demo_settings = {
        'batch_size': 4,
        'seq_len': 128,
        'model_dim': 256,
        'n_layers': 4,
        'n_heads': 4,
        'grad_accum': 2,
        'max_vocab': 3000
    }
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Quick tokenizer
    tokenizer = CustomTokenizer()
    vocab_size = tokenizer.build_vocab(CUSTOM_TRAINING_DATA[:3], min_freq=1, max_vocab=demo_settings['max_vocab'])
    
    # Small model
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=demo_settings['model_dim'],
        n_layers=demo_settings['n_layers'],
        n_heads=demo_settings['n_heads'],
        max_seq_len=demo_settings['seq_len']
    )
    
    # Quick dataset
    dataset = CustomDataset(CUSTOM_TRAINING_DATA[:3], tokenizer, demo_settings['seq_len'])
    
    # Quick training
    trainer = CustomTrainer(model, tokenizer, device)
    results = trainer.train(dataset, epochs=2, lr=5e-4, settings=demo_settings)
    
    # Quick test
    generated = trainer.generate("Machine learning", max_length=20)
    print(f"\nQuick test: '{generated}'")
    
    trainer.plot_training()
    return trainer, model, tokenizer

# UTILITY FUNCTIONS FOR GOOGLE COLAB
def load_model_from_drive(model_name="latest_model.pt"):
    """Load a trained model from Google Drive"""
    model_path = f"{PROJECT_DIR}/models/{model_name}"

    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        return None

    try:
        model_info = torch.load(model_path, map_location='cpu')

        # Recreate model
        config = model_info['model_config']
        model = CustomLanguageModel(
            vocab_size=config['vocab_size'],
            dim=config['dim'],
            max_seq_len=config['max_seq_len']
        )
        model.load_state_dict(model_info['model_state_dict'])

        # Load tokenizer
        tokenizer = CustomTokenizer()
        tokenizer.load("latest_tokenizer.json")

        print(f"✅ Model loaded successfully!")
        print(f"📊 Parameters: {model.count_params():,}")
        print(f"📚 Vocab size: {tokenizer.vocab_size:,}")

        return model, tokenizer

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def download_model_files():
    """Download model files from Google Drive to local machine"""
    if not IN_COLAB:
        print("⚠️ This function is only available in Google Colab")
        return

    try:
        # Create a zip file with all important files
        import zipfile

        zip_path = f"{PROJECT_DIR}/my_custom_model.zip"

        with zipfile.ZipFile(zip_path, 'w') as zipf:
            # Add model files
            for root, dirs, files in os.walk(f"{PROJECT_DIR}/models"):
                for file in files:
                    if file.endswith('.pt'):
                        file_path = os.path.join(root, file)
                        zipf.write(file_path, f"models/{file}")

            # Add tokenizer
            for root, dirs, files in os.walk(f"{PROJECT_DIR}/tokenizers"):
                for file in files:
                    if file.endswith('.json'):
                        file_path = os.path.join(root, file)
                        zipf.write(file_path, f"tokenizers/{file}")

            # Add logs
            for root, dirs, files in os.walk(f"{PROJECT_DIR}/logs"):
                for file in files[-5:]:  # Last 5 log files
                    file_path = os.path.join(root, file)
                    zipf.write(file_path, f"logs/{file}")

        # Download the zip file
        files.download(zip_path)
        print(f"📦 Model files downloaded as: my_custom_model.zip")

    except Exception as e:
        print(f"❌ Error creating download: {e}")

def interactive_chat(model=None, tokenizer=None, max_length=50):
    """Interactive chat with your trained model"""
    if model is None or tokenizer is None:
        print("🔄 Loading latest model...")
        result = load_model_from_drive()
        if result is None:
            print("❌ No model found. Train a model first!")
            return
        model, tokenizer = result

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    trainer = CustomTrainer(model, tokenizer, device)

    print("🤖 Interactive Chat Started!")
    print("💡 Type 'quit' to exit, 'help' for commands")
    print("-" * 50)

    while True:
        try:
            user_input = input("\n👤 You: ").strip()

            if user_input.lower() == 'quit':
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'help':
                print("Commands:")
                print("  quit - Exit chat")
                print("  help - Show this help")
                print("  temp <value> - Set temperature (0.1-2.0)")
                print("  length <value> - Set max generation length")
                continue
            elif user_input.lower().startswith('temp '):
                try:
                    temp = float(user_input.split()[1])
                    if 0.1 <= temp <= 2.0:
                        print(f"🌡️ Temperature set to {temp}")
                        continue
                    else:
                        print("❌ Temperature must be between 0.1 and 2.0")
                        continue
                except:
                    print("❌ Invalid temperature value")
                    continue
            elif user_input.lower().startswith('length '):
                try:
                    length = int(user_input.split()[1])
                    if 10 <= length <= 200:
                        max_length = length
                        print(f"📏 Max length set to {length}")
                        continue
                    else:
                        print("❌ Length must be between 10 and 200")
                        continue
                except:
                    print("❌ Invalid length value")
                    continue

            if user_input:
                print("🤖 AI: ", end="", flush=True)
                response = trainer.generate(user_input, max_length=max_length, temperature=0.8)
                print(response)

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

print("✅ CUSTOM MODEL TRAINER LOADED!")
print("🚀 Run: train_your_model() to start training from scratch!")
print("⚡ Or run: quick_demo() for a fast test!")
print("📝 Customize CUSTOM_TRAINING_DATA with your own text!")
print("🤖 Run: interactive_chat() to chat with your trained model!")
print("📦 Run: download_model_files() to download your model!")

if IN_COLAB:
    print("\n🔥 GOOGLE COLAB DETECTED!")
    print("📁 Google Drive will be mounted automatically")
    print("💾 All models and data will be saved to Drive")
    print("🔄 Training can be resumed if interrupted")

# EXAMPLE USAGE AND DOCUMENTATION
"""
🚀 QUICK START GUIDE:

1. BASIC TRAINING:
   trainer, model, tokenizer, results = train_your_model()

2. CUSTOM DATA TRAINING:
   my_data = ["Your custom text here...", "More training data..."]
   trainer, model, tokenizer, results = train_your_model(custom_data=my_data)

3. RESUME TRAINING:
   trainer, model, tokenizer, results = train_your_model(resume_from_checkpoint="path/to/checkpoint.pt")

4. INTERACTIVE CHAT:
   interactive_chat()  # Uses latest trained model

5. DOWNLOAD MODEL:
   download_model_files()  # Downloads zip file with all model files

📁 FILE STRUCTURE IN GOOGLE DRIVE:
/content/drive/MyDrive/CustomModelTraining/
├── models/          # Trained model files
├── tokenizers/      # Vocabulary files
├── checkpoints/     # Training checkpoints
├── logs/           # Training logs
└── plots/          # Training progress plots

🎯 TRAINING TIPS:
- Start with quick_demo() to test everything works
- Use your own text data for better results
- Training automatically saves to Google Drive
- You can resume training if interrupted
- Larger models need more data and time
- Monitor the loss curve for overfitting

🔧 CUSTOMIZATION:
- Modify CUSTOM_TRAINING_DATA with your text
- Adjust model size in get_optimal_settings()
- Change training parameters in train_your_model()
- Add your own text preprocessing in CustomTokenizer

⚠️ IMPORTANT NOTES:
- Free Colab has time limits (~12 hours)
- GPU memory is limited (~15GB on T4)
- Save frequently to avoid losing progress
- Use checkpoints for long training runs
"""

def example_training_workflow():
    """Example of a complete training workflow"""
    print("📚 EXAMPLE TRAINING WORKFLOW")
    print("=" * 50)

    # Step 1: Prepare your data
    my_custom_data = [
        "Your custom training text goes here. Make it relevant to your use case.",
        "Add more examples of the kind of text you want the model to generate.",
        "The more diverse and high-quality your data, the better your model will be.",
        # Add your own data here...
    ]

    # Step 2: Start training
    print("🚀 Starting training with custom data...")
    trainer, model, tokenizer, results = train_your_model(custom_data=my_custom_data)

    # Step 3: Test the model
    print("🧪 Testing the trained model...")
    test_prompts = ["Your test prompt", "Another test"]
    for prompt in test_prompts:
        response = trainer.generate(prompt, max_length=30)
        print(f"Prompt: {prompt}")
        print(f"Response: {response}\n")

    # Step 4: Start interactive chat
    print("💬 Starting interactive chat...")
    interactive_chat(model, tokenizer)

    return trainer, model, tokenizer

print("\n" + "="*60)
print("🎉 READY TO TRAIN YOUR OWN LANGUAGE MODEL!")
print("="*60)
print("📖 Read the documentation above for usage examples")
print("🚀 Run train_your_model() to get started!")
print("⚡ Run quick_demo() for a fast test!")
print("📚 Run example_training_workflow() for a complete example!")
print("="*60)