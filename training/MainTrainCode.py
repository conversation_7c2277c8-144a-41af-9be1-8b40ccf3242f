# 🚀 TRAIN YOUR OWN LANGUAGE MODEL FROM SCRATCH - FREE COLAB 🚀
# No GPT-2, no pretrained anything - 100% your own model!

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import os
import gc
from tqdm import tqdm
import matplotlib.pyplot as plt
import time
import json
import re
from collections import Counter, defaultdict

print("🔥 TRAIN YOUR OWN MODEL FROM SCRATCH!")
print(f"💾 GPU: {torch.cuda.get_device_name() if torch.cuda.is_available() else 'CPU only'}")
print(f"🔥 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB" if torch.cuda.is_available() else "")

# CUSTOM TOKENIZER - BUILD YOUR OWN VOCAB! 🛠️
class CustomTokenizer:
    def __init__(self):
        self.word_to_id = {}
        self.id_to_word = {}
        self.vocab_size = 0
        
        # Special tokens
        self.special_tokens = {
            '<PAD>': 0,
            '<UNK>': 1, 
            '<SOS>': 2,
            '<EOS>': 3,
        }
        
        for token, idx in self.special_tokens.items():
            self.word_to_id[token] = idx
            self.id_to_word[idx] = token
        
        self.vocab_size = len(self.special_tokens)
        print("🔧 Custom tokenizer initialized with special tokens")
    
    def build_vocab(self, texts, min_freq=2, max_vocab=10000):
        """Build vocabulary from your texts"""
        print("📚 Building vocabulary from scratch...")
        
        # Count word frequencies
        word_counts = Counter()
        total_words = 0
        
        for text in tqdm(texts, desc="Counting words"):
            # Simple word tokenization (you can make this fancier)
            words = self.simple_tokenize(text.lower())
            word_counts.update(words)
            total_words += len(words)
        
        print(f"📊 Found {len(word_counts)} unique words, {total_words:,} total words")
        
        # Filter by frequency and limit vocab size
        filtered_words = [word for word, count in word_counts.items() 
                         if count >= min_freq]
        
        # Sort by frequency and take top words
        most_common = word_counts.most_common(max_vocab - len(self.special_tokens))
        
        # Add words to vocabulary
        for word, count in most_common:
            if word not in self.word_to_id:
                self.word_to_id[word] = self.vocab_size
                self.id_to_word[self.vocab_size] = word
                self.vocab_size += 1
        
        print(f"🎯 Final vocabulary size: {self.vocab_size:,} tokens")
        print(f"📈 Coverage: {len(most_common)} words above min_freq={min_freq}")
        
        # Show some examples
        print("\n🔤 Sample vocabulary:")
        for i, (word, count) in enumerate(most_common[:20]):
            print(f"  {self.word_to_id[word]:4d}: '{word}' (count: {count})")
        
        return self.vocab_size
    
    def simple_tokenize(self, text):
        """Simple but effective tokenization"""
        # Clean and split text
        text = re.sub(r'[^\w\s.,!?;:\'"()-]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        
        # Split into words and punctuation
        tokens = []
        for word in text.split():
            # Split punctuation from words
            word = word.strip()
            if word:
                # Handle punctuation at the end
                while word and word[-1] in '.,!?;:':
                    if len(word) > 1:
                        tokens.append(word[:-1])
                        tokens.append(word[-1])
                        word = ""
                    else:
                        tokens.append(word)
                        word = ""
                
                if word:
                    tokens.append(word)
        
        return tokens
    
    def encode(self, text):
        """Convert text to token IDs"""
        words = self.simple_tokenize(text.lower())
        token_ids = []
        
        for word in words:
            token_ids.append(self.word_to_id.get(word, self.special_tokens['<UNK>']))
        
        return token_ids
    
    def decode(self, token_ids):
        """Convert token IDs back to text"""
        words = []
        for token_id in token_ids:
            if token_id in self.id_to_word:
                word = self.id_to_word[token_id]
                if word not in ['<PAD>', '<SOS>', '<EOS>']:
                    words.append(word)
        
        # Simple reconstruction
        text = ' '.join(words)
        # Fix punctuation spacing
        text = re.sub(r' ([.,!?;:])', r'\1', text)
        return text
    
    def save(self, path):
        """Save tokenizer"""
        vocab_data = {
            'word_to_id': self.word_to_id,
            'id_to_word': {str(k): v for k, v in self.id_to_word.items()},
            'vocab_size': self.vocab_size,
            'special_tokens': self.special_tokens
        }
        with open(path, 'w') as f:
            json.dump(vocab_data, f, indent=2)
        print(f"💾 Tokenizer saved to {path}")
    
    def load(self, path):
        """Load tokenizer"""
        with open(path, 'r') as f:
            vocab_data = json.load(f)
        
        self.word_to_id = vocab_data['word_to_id']
        self.id_to_word = {int(k): v for k, v in vocab_data['id_to_word'].items()}
        self.vocab_size = vocab_data['vocab_size']
        self.special_tokens = vocab_data['special_tokens']
        print(f"📁 Tokenizer loaded from {path}")

# YOUR OWN TRANSFORMER MODEL 🤖
class CustomLanguageModel(nn.Module):
    def __init__(self, vocab_size, dim=384, n_layers=6, n_heads=6, max_seq_len=256):
        super().__init__()
        self.vocab_size = vocab_size
        self.dim = dim
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.tok_emb = nn.Embedding(vocab_size, dim)
        self.pos_emb = nn.Embedding(max_seq_len, dim)
        self.dropout = nn.Dropout(0.1)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(dim, n_heads) for _ in range(n_layers)
        ])
        
        self.ln_f = nn.LayerNorm(dim)
        self.head = nn.Linear(dim, vocab_size)
        
        # Initialize weights
        self.apply(self._init_weights)
        
        print(f"🤖 Custom model created: {self.count_params():,} parameters")
        print(f"📐 Architecture: {n_layers} layers, {n_heads} heads, {dim} dim")
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def count_params(self):
        return sum(p.numel() for p in self.parameters())
    
    def forward(self, x, targets=None):
        B, T = x.shape
        
        # Get embeddings
        tok_emb = self.tok_emb(x)
        pos_emb = self.pos_emb(torch.arange(T, device=x.device))
        x = self.dropout(tok_emb + pos_emb)
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x)
        
        x = self.ln_f(x)
        logits = self.head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), targets.view(-1), ignore_index=0)
        
        return logits, loss

class TransformerBlock(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        self.attn = MultiHeadAttention(dim, n_heads)
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim),
            nn.Dropout(0.1)
        )
        self.ln1 = nn.LayerNorm(dim)
        self.ln2 = nn.LayerNorm(dim)
        self.dropout1 = nn.Dropout(0.1)
        self.dropout2 = nn.Dropout(0.1)
    
    def forward(self, x):
        x = x + self.dropout1(self.attn(self.ln1(x)))
        x = x + self.dropout2(self.mlp(self.ln2(x)))
        return x

class MultiHeadAttention(nn.Module):
    def __init__(self, dim, n_heads):
        super().__init__()
        assert dim % n_heads == 0
        
        self.n_heads = n_heads
        self.dim = dim
        self.head_dim = dim // n_heads
        
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.dropout = nn.Dropout(0.1)
    
    def forward(self, x):
        B, T, C = x.shape
        
        # Get Q, K, V
        qkv = self.qkv(x)
        q, k, v = qkv.chunk(3, dim=-1)
        
        # Reshape for multi-head attention
        q = q.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        k = k.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        v = v.view(B, T, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        att = (q @ k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        
        # Causal mask
        mask = torch.triu(torch.ones(T, T, device=x.device), diagonal=1).bool()
        att.masked_fill_(mask, float('-inf'))
        
        att = F.softmax(att, dim=-1)
        att = self.dropout(att)
        
        # Apply attention to values
        out = att @ v
        out = out.transpose(1, 2).contiguous().view(B, T, C)
        
        return self.proj(out)

# CUSTOM DATASET FOR YOUR MODEL
class CustomDataset(Dataset):
    def __init__(self, texts, tokenizer, seq_len=256):
        print("📝 Creating custom dataset...")
        self.seq_len = seq_len
        self.tokenizer = tokenizer
        
        # Tokenize all texts
        all_tokens = []
        for text in tqdm(texts, desc="Tokenizing"):
            tokens = tokenizer.encode(text)
            all_tokens.extend(tokens)
        
        print(f"📊 Total tokens: {len(all_tokens):,}")
        
        # Create sequences
        self.data = []
        for i in range(0, len(all_tokens) - seq_len, seq_len // 2):  # 50% overlap
            chunk = all_tokens[i:i + seq_len + 1]
            if len(chunk) == seq_len + 1:
                self.data.append(torch.tensor(chunk, dtype=torch.long))
        
        print(f"📦 Dataset ready: {len(self.data)} sequences of length {seq_len}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        chunk = self.data[idx]
        return chunk[:-1], chunk[1:]  # input, target

# FREE COLAB SETTINGS - OPTIMIZED FOR YOUR MODEL
def get_optimal_settings():
    """Get settings optimized for custom model on free Colab"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"🔍 GPU Memory: {gpu_memory:.1f} GB")
        
        if gpu_memory < 13:  # T4 GPU (free Colab)
            return {
                'batch_size': 8,
                'seq_len': 256,
                'model_dim': 384,
                'n_layers': 6,
                'n_heads': 6,
                'grad_accum': 2,
                'max_vocab': 8000
            }
        else:  # Better GPU
            return {
                'batch_size': 16,
                'seq_len': 512,
                'model_dim': 512,
                'n_layers': 8,
                'n_heads': 8,
                'grad_accum': 1,
                'max_vocab': 15000
            }
    else:  # CPU
        return {
            'batch_size': 2,
            'seq_len': 128,
            'model_dim': 256,
            'n_layers': 4,
            'n_heads': 4,
            'grad_accum': 4,
            'max_vocab': 5000
        }

# CUSTOM TRAINER - TRACKS EVERYTHING
class CustomTrainer:
    def __init__(self, model, tokenizer, device):
        self.model = model.to(device)
        self.tokenizer = tokenizer
        self.device = device
        self.losses = []
        self.step_times = []
        
        print(f"🚀 Custom trainer ready on {device}")
    
    def train(self, dataset, epochs=5, lr=3e-4, settings=None):
        if settings is None:
            settings = get_optimal_settings()
        
        print(f"🎯 Training settings: {settings}")
        
        # Setup
        dataloader = DataLoader(dataset, batch_size=settings['batch_size'], shuffle=True, pin_memory=True)
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        
        # Learning rate scheduler
        total_steps = len(dataloader) * epochs
        warmup_steps = min(500, total_steps // 10)
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=lr, total_steps=total_steps,
            pct_start=warmup_steps/total_steps
        )
        
        print(f"🚀 Starting training!")
        print(f"📊 {epochs} epochs × {len(dataloader)} batches = {total_steps:,} steps")
        print(f"🔥 Effective batch size: {settings['batch_size'] * settings['grad_accum']}")
        
        # Training loop
        self.model.train()
        global_step = 0
        best_loss = float('inf')
        
        start_time = time.time()
        
        for epoch in range(epochs):
            epoch_loss = 0
            epoch_start = time.time()
            
            pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}")
            optimizer.zero_grad()
            
            for batch_idx, (inputs, targets) in enumerate(pbar):
                step_start = time.time()
                
                inputs = inputs.to(self.device, non_blocking=True)
                targets = targets.to(self.device, non_blocking=True)
                
                # Forward pass
                logits, loss = self.model(inputs, targets)
                
                # Scale loss for gradient accumulation
                loss = loss / settings['grad_accum']
                loss.backward()
                
                epoch_loss += loss.item() * settings['grad_accum']
                
                # Gradient step
                if (batch_idx + 1) % settings['grad_accum'] == 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    optimizer.step()
                    scheduler.step()
                    optimizer.zero_grad()
                    global_step += 1
                
                # Track timing
                step_time = time.time() - step_start
                self.step_times.append(step_time)
                
                # Update progress
                avg_loss = epoch_loss / (batch_idx + 1)
                current_lr = optimizer.param_groups[0]['lr']
                
                pbar.set_postfix({
                    'loss': f'{avg_loss:.4f}',
                    'lr': f'{current_lr:.2e}',
                    'step/s': f'{1/step_time:.1f}'
                })
                
                # Memory cleanup
                if batch_idx % 50 == 0:
                    torch.cuda.empty_cache()
            
            # Epoch summary
            epoch_time = time.time() - epoch_start
            avg_epoch_loss = epoch_loss / len(dataloader)
            self.losses.append(avg_epoch_loss)
            
            print(f"✅ Epoch {epoch+1}: Loss={avg_epoch_loss:.4f}, Time={epoch_time:.1f}s")
            
            # Save best model
            if avg_epoch_loss < best_loss:
                best_loss = avg_epoch_loss
                torch.save(self.model.state_dict(), 'best_custom_model.pt')
                print(f"💾 New best model saved! Loss: {best_loss:.4f}")
        
        total_time = time.time() - start_time
        print(f"🎉 Training complete! Total time: {total_time/60:.1f} minutes")
        
        return {
            'losses': self.losses,
            'best_loss': best_loss,
            'total_time': total_time,
            'avg_step_time': np.mean(self.step_times)
        }
    
    def generate(self, prompt, max_length=50, temperature=0.8, top_k=40):
        """Generate text with your custom model!"""
        self.model.eval()
        
        tokens = self.tokenizer.encode(prompt)
        if not tokens:  # Empty prompt
            tokens = [self.tokenizer.special_tokens['<SOS>']]
        
        tokens = torch.tensor(tokens, device=self.device).unsqueeze(0)
        
        generated_tokens = []
        
        with torch.no_grad():
            for _ in range(max_length):
                # Truncate if too long
                if tokens.size(1) >= self.model.max_seq_len:
                    tokens = tokens[:, -(self.model.max_seq_len-1):]
                
                logits, _ = self.model(tokens)
                logits = logits[0, -1, :] / temperature
                
                # Top-k sampling
                if top_k > 0:
                    values, indices = torch.topk(logits, top_k)
                    logits = torch.full_like(logits, float('-inf'))
                    logits.scatter_(0, indices, values)
                
                probs = F.softmax(logits, dim=-1)
                next_token = torch.multinomial(probs, 1)
                
                # Stop at EOS token
                if next_token.item() == self.tokenizer.special_tokens['<EOS>']:
                    break
                
                tokens = torch.cat([tokens, next_token.unsqueeze(0)], dim=1)
                generated_tokens.append(next_token.item())
        
        # Decode generated text
        full_tokens = self.tokenizer.encode(prompt) + generated_tokens
        generated_text = self.tokenizer.decode(full_tokens)
        
        self.model.train()
        return generated_text
    
    def plot_training(self):
        """Plot training progress"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Loss curve
        if self.losses:
            ax1.plot(self.losses, 'b-', alpha=0.8, linewidth=2)
            ax1.set_title('Training Loss')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('Loss')
            ax1.grid(True, alpha=0.3)
        
        # Step timing
        if self.step_times:
            recent_times = self.step_times[-100:]  # Last 100 steps
            ax2.hist(recent_times, bins=20, alpha=0.7, color='green')
            ax2.set_title('Step Time Distribution')
            ax2.set_xlabel('Seconds per Step')
            ax2.set_ylabel('Count')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# SAMPLE TRAINING DATA - CUSTOMIZE THIS!
CUSTOM_TRAINING_DATA = [
    """
    The art of machine learning involves finding patterns in data that can generalize to new, unseen examples.
    Neural networks are powerful function approximators that can learn complex mappings between inputs and outputs.
    Deep learning uses multiple layers of neurons to learn hierarchical representations of data.
    Training a neural network involves adjusting weights through backpropagation and gradient descent.
    """,
    
    """
    Natural language processing enables computers to understand and generate human language.
    Transformers revolutionized NLP by using attention mechanisms to capture long-range dependencies.
    Language models learn to predict the next word in a sequence given the previous context.
    The quality of a language model depends on the size and diversity of its training data.
    """,
    
    """
    Artificial intelligence aims to create machines that can perform tasks requiring human intelligence.
    Machine learning is a subset of AI that learns from data without explicit programming.
    Supervised learning uses labeled examples to train predictive models.
    Unsupervised learning finds hidden patterns in unlabeled data.
    Reinforcement learning trains agents through rewards and penalties.
    """,
    
    """
    Computer vision enables machines to interpret and understand visual information.
    Convolutional neural networks are particularly effective for image recognition tasks.
    Object detection identifies and localizes multiple objects within images.
    Image segmentation assigns a class label to each pixel in an image.
    """,
    
    """
    The future of artificial intelligence holds great promise and challenges.
    Ethical AI development ensures that technology benefits humanity fairly.
    Explainable AI makes machine learning decisions more transparent and trustworthy.
    Edge AI brings intelligence to devices without requiring cloud connectivity.
    Quantum computing may revolutionize certain types of machine learning algorithms.
    """,
    
    # Add your own training data here!
    """
    Your custom training data goes here. Add as much text as you want!
    The model will learn the patterns, vocabulary, and style from this data.
    Make sure to include diverse examples of the kind of text you want to generate.
    """
] * 10  # Repeat for more training data

def train_your_model():
    """🚀 THE MAIN FUNCTION - TRAIN YOUR OWN MODEL FROM SCRATCH!"""
    print("🔥 TRAINING YOUR OWN MODEL FROM SCRATCH!")
    print("=" * 60)
    
    # Get optimal settings
    settings = get_optimal_settings()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Step 1: Build custom tokenizer
    print("\n📚 STEP 1: Building custom vocabulary...")
    tokenizer = CustomTokenizer()
    vocab_size = tokenizer.build_vocab(CUSTOM_TRAINING_DATA, min_freq=2, max_vocab=settings['max_vocab'])
    
    # Save tokenizer
    tokenizer.save('custom_tokenizer.json')
    
    # Step 2: Create custom model
    print(f"\n🤖 STEP 2: Creating your model...")
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=settings['model_dim'],
        n_layers=settings['n_layers'],
        n_heads=settings['n_heads'],
        max_seq_len=settings['seq_len']
    )
    
    # Step 3: Prepare dataset
    print("\n📦 STEP 3: Preparing training dataset...")
    dataset = CustomDataset(CUSTOM_TRAINING_DATA, tokenizer, settings['seq_len'])
    
    # Step 4: Train the model
    print("\n🚀 STEP 4: Training your model...")
    trainer = CustomTrainer(model, tokenizer, device)
    
    # Estimate training time
    estimated_time = len(dataset) * settings.get('grad_accum', 1) / settings['batch_size'] * 0.1 * 5  # 5 epochs
    print(f"⏰ Estimated training time: {estimated_time/60:.1f} minutes")
    
    results = trainer.train(dataset, epochs=5, lr=3e-4, settings=settings)
    
    # Step 5: Test generation
    print("\n🎭 STEP 5: Testing your model...")
    test_prompts = [
        "The future of",
        "Machine learning",
        "Artificial intelligence",
        "Neural networks",
    ]
    
    print("Generated samples:")
    print("-" * 40)
    for prompt in test_prompts:
        generated = trainer.generate(prompt, max_length=30, temperature=0.8)
        print(f"Prompt: '{prompt}'")
        print(f"Generated: '{generated}'")
        print()
    
    # Step 6: Show results
    print("📊 TRAINING SUMMARY:")
    print(f"✅ Final loss: {results['losses'][-1]:.4f}")
    print(f"⏰ Training time: {results['total_time']/60:.1f} minutes")
    print(f"⚡ Average step time: {results['avg_step_time']:.3f}s")
    print(f"🤖 Model parameters: {model.count_params():,}")
    print(f"📚 Vocabulary size: {vocab_size:,}")
    
    # Plot training curves
    trainer.plot_training()
    
    return trainer, model, tokenizer, results

def quick_demo():
    """Quick demo with minimal settings"""
    print("⚡ QUICK DEMO MODE - Minimal training for testing")
    
    # Use smaller settings for quick demo
    demo_settings = {
        'batch_size': 4,
        'seq_len': 128,
        'model_dim': 256,
        'n_layers': 4,
        'n_heads': 4,
        'grad_accum': 2,
        'max_vocab': 3000
    }
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Quick tokenizer
    tokenizer = CustomTokenizer()
    vocab_size = tokenizer.build_vocab(CUSTOM_TRAINING_DATA[:3], min_freq=1, max_vocab=demo_settings['max_vocab'])
    
    # Small model
    model = CustomLanguageModel(
        vocab_size=vocab_size,
        dim=demo_settings['model_dim'],
        n_layers=demo_settings['n_layers'],
        n_heads=demo_settings['n_heads'],
        max_seq_len=demo_settings['seq_len']
    )
    
    # Quick dataset
    dataset = CustomDataset(CUSTOM_TRAINING_DATA[:3], tokenizer, demo_settings['seq_len'])
    
    # Quick training
    trainer = CustomTrainer(model, tokenizer, device)
    results = trainer.train(dataset, epochs=2, lr=5e-4, settings=demo_settings)
    
    # Quick test
    generated = trainer.generate("Machine learning", max_length=20)
    print(f"\nQuick test: '{generated}'")
    
    trainer.plot_training()
    return trainer, model, tokenizer

print("✅ CUSTOM MODEL TRAINER LOADED!")
print("🚀 Run: train_your_model() to start training from scratch!")
print("⚡ Or run: quick_demo() for a fast test!")
print("📝 Customize CUSTOM_TRAINING_DATA with your own text!")