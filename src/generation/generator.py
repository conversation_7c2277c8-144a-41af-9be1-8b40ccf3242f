"""
Featureimanbug AI - Main text generation interface.
"""
import torch
import t<PERSON><PERSON><PERSON>
from typing import Optional, Union, List, Dict, Any
import time

from .strategies import (
    GenerationStrategy, GreedyDecoding, TemperatureSampling, 
    TopKSampling, TopPSampling, BeamSearch
)
from ..model.gpt import GPTModel


class TextGenerator:
    """
    Featureimanbug AI text generator with multiple sampling strategies.
    """
    
    def __init__(self, model, tokenizer):
        self.model = model
        self.tokenizer = tokenizer
        self.device = next(model.parameters()).device

        # Check if this is a custom tokenizer
        self.is_custom_tokenizer = hasattr(tokenizer, 'word_to_id')
        
        # Available generation strategies
        self.strategies = {
            'greedy': GreedyDecoding(),
            'temperature': TemperatureSampling,
            'top_k': TopKSampling,
            'top_p': TopPSampling,
            'beam_search': BeamSearch
        }
    
    def generate(self, 
                 prompt: str,
                 max_new_tokens: int = 100,
                 strategy: str = 'temperature',
                 temperature: float = 0.8,
                 top_k: Optional[int] = 50,
                 top_p: Optional[float] = 0.9,
                 beam_width: int = 5,
                 length_penalty: float = 1.0,
                 repetition_penalty: float = 1.0,
                 stop_tokens: Optional[List[str]] = None,
                 stream: bool = False) -> Union[str, List[str]]:
        """
        Generate text using the specified strategy.
        
        Args:
            prompt: Input text prompt
            max_new_tokens: Maximum number of tokens to generate
            strategy: Generation strategy ('greedy', 'temperature', 'top_k', 'top_p', 'beam_search')
            temperature: Temperature for sampling (higher = more random)
            top_k: Number of top tokens to consider for top-k sampling
            top_p: Cumulative probability threshold for nucleus sampling
            beam_width: Number of beams for beam search
            length_penalty: Length penalty for beam search
            repetition_penalty: Penalty for repeating tokens
            stop_tokens: List of stop tokens to end generation
            stream: Whether to stream tokens as they're generated
            
        Returns:
            Generated text string or list of strings (for beam search)
        """
        # Encode prompt - handle both custom and tiktoken tokenizers
        if self.is_custom_tokenizer:
            # Custom tokenizer from MainTrainCode.py
            token_ids = self.tokenizer.encode(prompt)
        else:
            # Tiktoken tokenizer
            token_ids = self.tokenizer.encode(prompt, allowed_special={"<|endoftext|>"})

        input_ids = torch.tensor(
            token_ids,
            dtype=torch.long,
            device=self.device
        ).unsqueeze(0)
        
        # Initialize generation strategy
        if strategy == 'greedy':
            gen_strategy = GreedyDecoding()
        elif strategy == 'temperature':
            gen_strategy = TemperatureSampling(temperature)
        elif strategy == 'top_k':
            gen_strategy = TopKSampling(top_k, temperature)
        elif strategy == 'top_p':
            gen_strategy = TopPSampling(top_p, temperature)
        elif strategy == 'beam_search':
            gen_strategy = BeamSearch(beam_width, length_penalty)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        # Handle beam search separately
        if strategy == 'beam_search':
            return self._beam_search_generate(
                input_ids, max_new_tokens, gen_strategy, stop_tokens
            )
        
        # Standard generation
        if stream:
            return self._stream_generate(
                input_ids, max_new_tokens, gen_strategy, 
                repetition_penalty, stop_tokens
            )
        else:
            return self._standard_generate(
                input_ids, max_new_tokens, gen_strategy,
                repetition_penalty, stop_tokens
            )
    
    def _standard_generate(self, 
                          input_ids: torch.Tensor,
                          max_new_tokens: int,
                          strategy: GenerationStrategy,
                          repetition_penalty: float,
                          stop_tokens: Optional[List[str]]) -> str:
        """Standard text generation."""
        self.model.eval()
        generated_ids = input_ids.clone()
        
        with torch.no_grad():
            for _ in range(max_new_tokens):
                # Get model predictions
                logits, _ = self.model(generated_ids)
                logits = logits[:, -1, :]  # Last token logits
                
                # Apply repetition penalty
                if repetition_penalty != 1.0:
                    logits = self._apply_repetition_penalty(
                        logits, generated_ids, repetition_penalty
                    )
                
                # Sample next token
                next_token = strategy.sample(logits)
                generated_ids = torch.cat([generated_ids, next_token], dim=1)
                
                # Check for stop tokens
                if stop_tokens:
                    generated_text = self.tokenizer.decode(generated_ids[0].tolist())
                    if any(stop_token in generated_text for stop_token in stop_tokens):
                        break
                
                # Truncate context if too long
                max_length = getattr(self.model, 'max_seq_len', getattr(self.model, 'cfg', {}).get('context_length', 1024))
                if generated_ids.size(1) > max_length:
                    generated_ids = generated_ids[:, -max_length:]
        
        # Decode and return - handle both tokenizer types
        if self.is_custom_tokenizer:
            generated_text = self.tokenizer.decode(generated_ids[0].tolist())
        else:
            generated_text = self.tokenizer.decode(generated_ids[0].tolist())
        return generated_text
    
    def _stream_generate(self,
                        input_ids: torch.Tensor,
                        max_new_tokens: int,
                        strategy: GenerationStrategy,
                        repetition_penalty: float,
                        stop_tokens: Optional[List[str]]):
        """Stream generation - yields tokens as they're generated."""
        self.model.eval()
        generated_ids = input_ids.clone()
        
        with torch.no_grad():
            for _ in range(max_new_tokens):
                # Get model predictions
                logits, _ = self.model(generated_ids)
                logits = logits[:, -1, :]
                
                # Apply repetition penalty
                if repetition_penalty != 1.0:
                    logits = self._apply_repetition_penalty(
                        logits, generated_ids, repetition_penalty
                    )
                
                # Sample next token
                next_token = strategy.sample(logits)
                generated_ids = torch.cat([generated_ids, next_token], dim=1)
                
                # Decode and yield new token
                new_text = self.tokenizer.decode([next_token.item()])
                yield new_text
                
                # Check for stop tokens
                if stop_tokens and any(stop_token in new_text for stop_token in stop_tokens):
                    break
                
                # Truncate context if too long
                if generated_ids.size(1) > self.model.cfg.context_length:
                    generated_ids = generated_ids[:, -self.model.cfg.context_length:]
    
    def _beam_search_generate(self,
                             input_ids: torch.Tensor,
                             max_new_tokens: int,
                             beam_search: BeamSearch,
                             stop_tokens: Optional[List[str]]) -> List[str]:
        """Beam search generation."""
        end_token = self.tokenizer.encode("<|endoftext|>")[0] if stop_tokens else None
        
        sequences = beam_search.search(
            self.model, input_ids, 
            input_ids.size(1) + max_new_tokens, 
            end_token
        )
        
        # Decode sequences
        results = []
        for sequence, score in sequences:
            text = self.tokenizer.decode(sequence)
            results.append(text)
        
        return results
    
    def _apply_repetition_penalty(self,
                                 logits: torch.Tensor,
                                 generated_ids: torch.Tensor,
                                 penalty: float) -> torch.Tensor:
        """Apply repetition penalty to logits."""
        for token_id in set(generated_ids[0].tolist()):
            if logits[0, token_id] > 0:
                logits[0, token_id] /= penalty
            else:
                logits[0, token_id] *= penalty
        return logits
    
    def calculate_perplexity(self, text: str) -> float:
        """Calculate perplexity of the given text."""
        tokens = self.tokenizer.encode(text, allowed_special={"<|endoftext|>"})
        if len(tokens) < 2:
            return float('inf')
        
        input_ids = torch.tensor(tokens, device=self.device).unsqueeze(0)
        
        with torch.no_grad():
            logits, _ = self.model(input_ids)
            
        # Calculate cross-entropy loss
        shift_logits = logits[..., :-1, :].contiguous()
        shift_labels = input_ids[..., 1:].contiguous()
        
        loss = torch.nn.functional.cross_entropy(
            shift_logits.view(-1, shift_logits.size(-1)),
            shift_labels.view(-1),
            reduction='mean'
        )
        
        return torch.exp(loss).item()
